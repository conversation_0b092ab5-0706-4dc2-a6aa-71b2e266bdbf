<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="async">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/async-2.11.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/characters-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/clock-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/collection-1.18.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="cupertino_icons">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/fake_async-1.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_lints">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="lints">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/lints-2.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/matcher-0.12.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/material_color_utilities-0.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/meta-1.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/path-1.8.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/test_api-0.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/web-0.3.0/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/async-2.11.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/characters-1.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/clock-1.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/collection-1.18.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.6/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/fake_async-1.3.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/flutter_lints-2.0.3/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/lints-2.1.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/matcher-0.12.16/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/material_color_utilities-0.5.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/meta-1.10.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/path-1.8.3/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/source_span-1.10.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/test_api-0.6.1/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/.pub-cache/hosted/pub.dev/web-0.3.0/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/packages/flutter/lib" />
      <root url="file://$PROJECT_DIR$/../../../../Develop/SDK/Flutter_SDK/packages/flutter_test/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>